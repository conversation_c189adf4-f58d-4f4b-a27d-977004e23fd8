# Bedalator Project Structure

This document provides a comprehensive overview of the Bedalator Chrome extension project structure and organization.

## 📁 Directory Overview

### Root Directory
```
bedalator/
├── 📄 manifest.json          # Chrome extension configuration
├── 📄 content.js             # Content script for HTML parsing
├── 📄 background.js           # Service worker for data management
├── 📄 popup.html             # User interface layout
├── 📄 popup.js               # Popup logic and communication
├── 📄 popup.css              # Styling for popup interface
├── 📄 README.md              # Main project documentation
├── 📄 PROJECT_STRUCTURE.md   # This file
├── 📁 icons/                 # Extension icons
├── 📁 ai_docs/               # Development documentation
├── 📁 specs/                 # Project specifications
├── 📁 .prompts/              # AI development context
└── 📁 .idea/                 # IDE configuration files
```

## 🔧 Core Extension Files

### `manifest.json`
**Purpose**: Chrome extension configuration file
- Defines extension metadata (name, version, description in German)
- Specifies permissions (storage, activeTab, alarms, tabs)
- Configures content scripts and service worker
- Sets up popup and icon references
- Uses Manifest V3 format

### `content.js`
**Purpose**: Content script that runs on web pages
- Contains `BedalatorTimeTracker` class
- Parses HTML time data from work tracking systems
- Monitors DOM for real-time updates
- Detects work status from button text
- Calculates daily work totals
- Communicates with background script

### `background.js`
**Purpose**: Service worker for extension lifecycle management
- Contains `BedalatorBackground` class
- Handles data persistence using Chrome Storage API
- Manages work history (30-day retention)
- Updates extension badge with current time
- Handles tab management and cleanup
- Processes messages from content and popup scripts

### `popup.html`
**Purpose**: User interface structure
- German language interface layout
- Black-on-black modern design
- Fixed header with status indicator
- Scrollable session list container
- Footer with update information
- 360x650px optimized dimensions

### `popup.js`
**Purpose**: Popup interface logic
- Contains `BedalatorPopup` class
- Handles automatic data loading
- Manages 30-second auto-refresh
- Communicates with content and background scripts
- Displays German error messages
- Handles cross-tab functionality

### `popup.css`
**Purpose**: User interface styling
- Modern dark theme (#0a0a0a background)
- SF Mono typography for time displays
- Animated status indicators
- Responsive layout design
- Smooth scrolling and transitions
- Professional appearance

## 🎨 Assets Directory

### `icons/`
**Purpose**: Extension icons for different contexts
```
icons/
├── icon16.png    # 16x16 - Browser toolbar
├── icon32.png    # 32x32 - Additional size
├── icon48.png    # 48x48 - Extension management
└── icon128.png   # 128x128 - Chrome Web Store
```
- Professional PNG format icons
- Modern design suitable for workplace environments
- Proper sizing for all Chrome extension contexts

## 📚 Documentation Directories

### `ai_docs/`
**Purpose**: Development documentation for AI assistance
```
ai_docs/
└── DEVELOPMENT.md    # Comprehensive development guide
```
- Technical implementation details
- Class and function documentation
- Testing guidelines and debugging instructions
- Code examples and best practices
- Development workflow and setup instructions

### `specs/`
**Purpose**: Project specifications and requirements
```
specs/
└── init.md    # Original project specifications
```
- Original feature requirements
- Technical architecture specifications
- Implementation status tracking
- German language interface requirements
- UI/UX design specifications

### `.prompts/`
**Purpose**: AI development context and prompts
```
.prompts/
└── context_prime.md    # Context primer for AI assistance
```
- Project understanding guidelines
- Context for AI development assistance
- Quick reference for project structure

### `.idea/`
**Purpose**: IDE configuration files (WebStorm/IntelliJ)
```
.idea/
├── .gitignore
├── AugmentWebviewStateStore.xml
├── Bedalator.iml
├── modules.xml
└── vcs.xml
```
- IDE-specific configuration files
- Project settings and module definitions
- Version control system configuration

## 🔄 Data Flow Architecture

### 1. Content Script → Background Script
- Time data extraction and status detection
- Real-time updates every 30 seconds
- Work session calculations

### 2. Background Script → Chrome Storage
- Data persistence and history management
- Badge updates and tab tracking
- Cleanup of old data (30+ days)

### 3. Popup → Content Script
- Fresh data requests
- Page compatibility checking
- Real-time status updates

### 4. Popup → Background Script
- Stored data retrieval
- History access
- Tab availability checking

## 🎯 Key Features by File

### Time Tracking (`content.js`)
- HTML table parsing (`#DataGridVorhandeneBuchungen`)
- Status detection (`#divTerminalbuttons`)
- Real-time calculations
- Session duration tracking

### Data Management (`background.js`)
- Chrome Storage API integration
- 30-day history retention
- Badge updates
- Tab lifecycle management

### User Interface (`popup.html/js/css`)
- German language interface
- Automatic data loading
- 30-second auto-refresh
- Cross-tab functionality
- Modern dark theme design

## 🔧 Development Workflow

1. **Setup**: Load extension in Chrome developer mode
2. **Development**: Edit source files and reload extension
3. **Testing**: Use work tracking page to test functionality
4. **Debugging**: Use Chrome DevTools and extension console
5. **Documentation**: Update relevant .md files

## 📋 File Dependencies

### Content Script Dependencies
- DOM elements: `#DataGridVorhandeneBuchungen`, `#divTerminalbuttons`
- Chrome APIs: `chrome.runtime.sendMessage`

### Background Script Dependencies
- Chrome APIs: `chrome.storage.local`, `chrome.action.setBadgeText`
- Message handling from content and popup scripts

### Popup Dependencies
- Chrome APIs: `chrome.runtime.sendMessage`, `chrome.tabs.query`
- Communication with content and background scripts

This structure ensures a clean separation of concerns while maintaining efficient communication between all components of the extension.
