/**
 * Bedalator Popup Styles
 * Sleek black-on-black modern design
 */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  overflow: hidden !important; /* Force prevent html from scrolling */
  height: 650px !important; /* Fixed height */
  width: 360px !important; /* Fixed width */
  max-height: 650px !important;
  max-width: 360px !important;
  position: fixed !important; /* Completely lock position */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Inter', sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #ffffff;
  background: #0a0a0a;
  width: 360px !important;
  height: 650px !important;
  max-width: 360px !important;
  max-height: 650px !important;
  font-weight: 400;
  overflow: hidden !important; /* Force prevent body from scrolling */
  margin: 0 !important;
  padding: 0 !important;
  position: fixed !important; /* Completely lock position */
  top: 0 !important;
  left: 0 !important;
}

.container {
  display: flex;
  flex-direction: column;
  height: 650px !important; /* Force fixed height */
  width: 360px !important; /* Force fixed width */
  max-height: 650px !important;
  max-width: 360px !important;
  background: #0a0a0a;
  overflow: hidden !important; /* Force prevent container from scrolling */
  position: relative;
}

/* Header */
.header {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  color: #ffffff;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333333;
  flex-shrink: 0; /* Keep header fixed */
}

.logo h1 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 2px;
  letter-spacing: -0.5px;
}

.logo .subtitle {
  font-size: 11px;
  opacity: 0.7;
  color: #888888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  padding: 8px 14px;
  border-radius: 12px;
  font-size: 11px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.working {
  background: #00ff88;
  box-shadow: 0 0 12px rgba(0, 255, 136, 0.4);
  animation: pulse 2s infinite;
}

.status-dot.not-working {
  background: #ff4757;
  box-shadow: 0 0 8px rgba(255, 71, 87, 0.3);
}

.status-dot.unknown {
  background: #666666;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Time Display */
.time-display {
  padding: 28px 24px;
  background: #111111;
  border-bottom: 1px solid #333333;
  flex-shrink: 0; /* Prevent time display from shrinking */
}

.total-time, .current-session {
  text-align: center;
  margin-bottom: 20px;
}

.total-time label, .current-session label {
  display: block;
  font-size: 11px;
  color: #888888;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.time-value {
  font-size: 42px;
  font-weight: 200;
  color: #ffffff;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: -1px;
}

.time-value.current {
  font-size: 28px;
  color: #00ff88;
  font-weight: 300;
}

/* Sessions Section */
.sessions-section {
  flex: 1;
  padding: 16px 24px 0 24px; /* Reduced top padding to make more compact */
  background: #0a0a0a;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex child to shrink */
  overflow: hidden !important; /* Force prevent section from scrolling */
  max-height: calc(650px - 280px); /* Reserve more space for header, time display, and footer */
}

.sessions-section h3 {
  font-size: 13px; /* Slightly smaller font */
  margin-bottom: 12px; /* Reduced margin */
  color: #ffffff;
  border-bottom: 1px solid #333333;
  padding-bottom: 8px; /* Reduced padding */
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  flex-shrink: 0; /* Prevent header from shrinking */
}

.sessions-container {
  flex: 1;
  overflow-y: auto !important; /* Force only vertical scrolling for sessions */
  overflow-x: hidden !important; /* Force no horizontal scrolling */
  padding-bottom: 16px; /* Reduced padding */
  min-height: 180px; /* Reduced minimum height but still show ~3 entries */
  max-height: 100%; /* Constrain to available space */
  scroll-behavior: smooth; /* Smooth scrolling */
  /* Ensure this is the ONLY scrollable area */
}

.sessions-list {
  /* Let parent container handle all scrolling */
  overflow: visible !important;
  height: auto;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px; /* Slightly reduced padding for more compact layout */
  margin-bottom: 8px; /* Reduced margin for more compact layout */
  background: #1a1a1a;
  border-radius: 8px; /* Slightly smaller border radius */
  border-left: 3px solid #333333;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  min-height: 50px; /* Ensure consistent height */
  max-height: 60px; /* Prevent items from getting too tall */
}

.session-item:hover {
  background: #222222;
  border-left-color: #555555;
  transform: translateY(-1px);
}

.session-item.active {
  border-left-color: #00ff88;
  background: rgba(0, 255, 136, 0.05);
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.1);
}

.session-times {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
}

.separator {
  color: #666666;
  font-weight: 300;
}

.end-time.active {
  color: #00ff88;
  font-weight: 500;
}

.session-duration {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 500;
  color: #cccccc;
  font-size: 13px;
}

.no-sessions {
  text-align: center;
  color: #666666;
  font-style: italic;
  padding: 40px 20px;
  font-size: 13px;
}





/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 16px 20px;
  background: #667eea;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow: hidden;
}

/* History */
.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  display: flex;
  flex-direction: column;
}

.day-name {
  font-weight: 500;
  color: #333;
}

.date {
  font-size: 12px;
  color: #666;
}

.history-time {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  font-size: 16px;
  color: #667eea;
}

.history-sessions {
  font-size: 12px;
  color: #666;
}

.no-history {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 20px;
}

/* Error Message */
.error-message {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  padding: 16px 20px;
  margin: 16px 24px;
  border-radius: 10px;
  border: 1px solid rgba(255, 71, 87, 0.2);
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  flex-shrink: 0; /* Keep error message fixed */
  position: relative; /* Ensure it stays in place */
  overflow: hidden; /* No scrolling for error messages */
}

.error-icon {
  font-size: 18px;
}

/* Loading Indicator */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #888888;
  flex-shrink: 0; /* Keep loading indicator fixed */
  position: relative; /* Ensure it stays in place */
  overflow: hidden; /* No scrolling for loading indicator */
}

.spinner {
  width: 28px;
  height: 28px;
  border: 2px solid #333333;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Footer */
.footer {
  padding: 16px 24px;
  background: #111111;
  border-top: 1px solid #333333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #666666;
  flex-shrink: 0; /* Keep footer fixed */
}

.auto-update-indicator {
  color: #00ff88;
  margin-left: 8px;
  animation: pulse 2s infinite;
  font-size: 8px;
}

/* FORCE NO scrollbars on main elements - use !important to override everything */
html::-webkit-scrollbar,
body::-webkit-scrollbar,
.container::-webkit-scrollbar,
.header::-webkit-scrollbar,
.time-display::-webkit-scrollbar,
.sessions-section::-webkit-scrollbar,
.footer::-webkit-scrollbar,
.error-message::-webkit-scrollbar,
.loading::-webkit-scrollbar {
  display: none !important; /* Force hide scrollbars completely */
  width: 0 !important;
  height: 0 !important;
}

html, body, .container, .header, .time-display, .sessions-section, .footer, .error-message, .loading {
  -ms-overflow-style: none !important; /* IE and Edge */
  scrollbar-width: none !important; /* Firefox */
  overflow-x: hidden !important; /* Force no horizontal scrolling */
  overflow-y: hidden !important; /* Force no vertical scrolling */
}

/* Override any potential scrolling behavior */
html, body, .container {
  overscroll-behavior: none !important; /* Prevent overscroll */
  touch-action: none !important; /* Prevent touch scrolling */
}

/* ONLY sessions-container should have scrollbar */
.sessions-container::-webkit-scrollbar {
  width: 6px;
}

.sessions-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 3px;
}

.sessions-container::-webkit-scrollbar-thumb {
  background: #444444;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.sessions-container::-webkit-scrollbar-thumb:hover {
  background: #666666;
}

/* Ensure sessions-container can scroll but nothing else */
.sessions-container {
  -ms-overflow-style: auto !important; /* Allow scrollbar in IE/Edge */
  scrollbar-width: thin !important; /* Allow thin scrollbar in Firefox */
  overflow-x: hidden !important;
  overflow-y: auto !important;
  overscroll-behavior: contain !important; /* Contain scrolling to this element */
}

/* Responsive adjustments */
@media (max-height: 600px) {
  .time-value {
    font-size: 28px;
  }
}
