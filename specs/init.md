# Bedalator - Chrome Extension Specifications

**Status: IMPLEMENTED** ✅

This document contains the original specifications for the Bedalator Chrome extension. The extension has been successfully implemented and is production-ready.

## Overview
This specification outlines the development of a Chrome extension that calculates daily work hours by parsing HTML time data from a work tracking system. The extension provides real-time calculation with data persistence, features a sleek black-on-black modern UI design, and uses German language throughout the interface.

**Implementation Status**: All core features have been implemented and are fully functional.

## Core Features - Implementation Status

### Time Tracking Logic ✅ IMPLEMENTED
- ✅ Parse completed work sessions from HTML table (`#DataGridVorhandeneBuchungen`)
- ✅ Detect active work sessions when the second column is empty
- ✅ Real-time calculation of current session duration with data persistence
- ✅ Daily total combining completed and ongoing sessions
- ✅ **Automatic Data Loading**: Extension automatically loads and displays data when popup opens
- ✅ **Enhanced Active Session Calculation**: Finds most recent incomplete session for accurate ongoing time
- ✅ **Cross-Tab Functionality**: Continues tracking even when time tracking tab is not active
- ✅ **Background Time Calculation**: Persistent updates regardless of tab visibility or browser focus
- ✅ **Intelligent Refresh**: 30-second background updates with visibility-aware frequency adjustment
- ✅ **Data Persistence**: 30-day history retention using Chrome Storage API

### Status Detection ✅ IMPLEMENTED
- ✅ "Kommen" button = Not currently working (Inaktiv)
- ✅ "Gehen" button = Currently working (Aktiv)
- ✅ Use button text to determine work status from `#divTerminalbuttons`
- ✅ Visual status indicators with animated effects (🟢 Aktiv, 🔴 Inaktiv, ⚪ Unbekannt)

### Language & Localization ✅ IMPLEMENTED
- ✅ Complete German language interface
- ✅ German time format and locale support
- ✅ Localized status messages and error handling
- ✅ German labels for all UI components ("Gesamte Tageszeit", "Aktuelle Sitzung")

## Technical Architecture

### Content Script (content.js)
```javascript
// Key functions implemented:
- extractTimeData() // Extract time pairs from HTML table
- calculateDailyTotal() // Calculate total work time in real-time
- getCurrentSessionDuration() // Calculate ongoing session if active
- getCurrentWorkStatus() // Check button text for current status
- collectWorkData() // Gather all work information without storage
```

### Background Script (background.js)
```javascript
// Handles:
- Extension lifecycle management
- Real-time badge updates
- Temporary data storage for popup communication
- German language support in notifications
```

### Popup Interface (popup.html)
```html
<!-- German UI displaying:
- Gesamte Tageszeit (Total daily time)
- Aktuelle Sitzung (Current session duration if working)
- Aktuelle Sitzungen (List of current sessions)
- Status indicator (Aktiv/Inaktiv/Unbekannt)
- Single scrollable container for improved UX
-->
```

### UI/UX Design Specifications
- **Color Scheme**: Black-on-black modern design (#0a0a0a background)
- **Typography**: SF Mono for time displays, Inter for UI text
- **Animations**: Pulsing green dot for active status
- **Scrolling**: Single consolidated scrollable container with optimized height
- **Responsive**: 360px width, 580px height (increased for better content visibility)
- **Layout Optimization**: Reduced padding and margins to maximize session list space
- **Session Display**: Compact session items with improved spacing for more visible entries
- **Accessibility**: High contrast, clear visual hierarchy
## Implementation Steps

### Phase 1: HTML Parsing & Real-time Processing
- Identify data table using selector: `#DataGridVorhandeneBuchungen table tbody`
- Extract time pairs from each row's two cells
- Parse time format (HH:MM) into minutes for calculations
- Handle empty second columns (ongoing sessions)
- Real-time calculation without persistent storage

### Phase 2: Time Calculations
Calculate completed sessions:
```javascript
completedMinutes = endTime - startTime (for each complete pair)
```

Calculate current session (if second column empty):
```javascript
currentMinutes = currentTime - lastStartTime
```

Format display time as HH:MM with German locale

### Phase 3: Status Detection & German UI
- Monitor button text in `#divTerminalbuttons`
- Check for "Gehen" = currently working (Aktiv)
- Check for "Kommen" = not working (Inaktiv)
- Update calculations and UI based on status
- Display German status messages

### Phase 4: Extension Integration & UI Optimization
- **Robust Content Script Injection**: Multiple initialization attempts with element detection
- **Enhanced Message Passing**: Timeout protection and error handling for popup-content communication
- **Automatic Data Loading**: Popup immediately loads fresh data on open without manual refresh
- **Intelligent Updates**: 15-second refresh cycles with status-aware frequency
- **Optimized Layout**: 580px height with reduced padding for maximum session visibility
- **Error Handling**: German error messages with page compatibility checking
- **Single Scrollable Container**: Consolidated sessions display with improved scrolling UX

## Implemented File Structure ✅
```
bedalator/
├── manifest.json          # ✅ Extension config with German description
├── content.js             # ✅ BedalatorTimeTracker class implementation
├── background.js           # ✅ BedalatorBackground service worker
├── popup.html             # ✅ German UI with black-on-black design
├── popup.js               # ✅ BedalatorPopup with auto-refresh
├── popup.css              # ✅ Modern dark theme styling
├── icons/                 # ✅ Professional PNG icons
│   ├── icon16.png         # ✅ 16x16 toolbar icon
│   ├── icon32.png         # ✅ 32x32 additional size
│   ├── icon48.png         # ✅ 48x48 extension management
│   └── icon128.png        # ✅ 128x128 Chrome Web Store
├── ai_docs/               # ✅ Development documentation
│   └── DEVELOPMENT.md     # ✅ Comprehensive dev guide
├── specs/                 # ✅ Project specifications
│   └── init.md           # ✅ This file
└── .prompts/              # ✅ AI development context
    └── context_prime.md   # ✅ Context primer
```
Key Functions to Implement
Data Extraction
javascript
function extractTimeData() {
const rows = document.querySelectorAll('#DataGridVorhandeneBuchungen tbody tr:not(.dx-freespace-row)');
return Array.from(rows).map(row => {
const cells = row.querySelectorAll('td');
return {
start: cells[0]?.textContent.trim(),
end: cells[1]?.textContent.trim() || null
};
});
}
Work Status Detection
javascript
function getCurrentWorkStatus() {
const button = document.querySelector('#divTerminalbuttons .dx-button-text');
return button?.textContent === 'Gehen' ? 'working' : 'not-working';
}
Time Calculation
javascript
function calculateDailyTotal(timeData, currentStatus) {
let totalMinutes = 0;

// Add completed sessions
timeData.forEach(session => {
if (session.start && session.end) {
totalMinutes += timeToMinutes(session.end) - timeToMinutes(session.start);
}
});

// Add current session if working
if (currentStatus === 'working') {
const lastIncomplete = timeData.find(s => s.start && !s.end);
if (lastIncomplete) {
const now = new Date();
const currentMinutes = now.getHours() * 60 + now.getMinutes();
totalMinutes += currentMinutes - timeToMinutes(lastIncomplete.start);
}
}

return totalMinutes;
}
## User Interface Design

### Popup Display (German Interface)
- Large timer display showing "Gesamte Tageszeit" (total daily hours)
- Current session timer "Aktuelle Sitzung" (if actively working)
- Session list "Aktuelle Sitzungen" with start/end times
- Status indicator (Aktiv/Inaktiv/Unbekannt)
- "Aktualisieren" button to refresh data
- Single consolidated scrollable container

### Visual Elements & Design System
- **Active Status**: Pulsing green indicator (#00ff88) with animation
- **Inactive Status**: Red indicator (#ff4757)
- **Unknown Status**: Gray indicator (#666666)
- **Background**: Deep black (#0a0a0a) with subtle gradients
- **Typography**: SF Mono for time displays, clean sans-serif for UI
- **Layout**: 360px × 520px responsive design
- **Scrolling**: Optimized single-container scrolling experience
- **Accessibility**: High contrast ratios, clear visual hierarchy

### German Language Elements
- All labels and messages in German
- German time format (de-DE locale)
- Localized error messages and status indicators
- German button text and tooltips

## Data Management Strategy

### Real-time Calculation Approach
- **No Historical Storage**: Extension calculates time in real-time only
- **Temporary Session Data**: Only current session data stored temporarily
- **Cross-Tab Persistence**: Calculations continue regardless of active tab
- **Background Processing**: Continuous updates even when tab is hidden
- **Memory Efficient**: Minimal data footprint, no long-term storage
- **Privacy Focused**: No persistent user data collection

### Cross-Tab Architecture
- **Tab Registry**: Background script maintains registry of tracking tabs
- **Visibility-Aware Updates**: Different update frequencies for visible/hidden tabs
- **Universal Access**: Popup works from any tab, not just tracking page
- **Automatic Cleanup**: Self-managing tab lifecycle with closed tab removal
- **Fault Tolerance**: Multiple fallback data sources for reliability

### Tab Closure Handling
- **Real-time Detection**: Immediate detection when tracking tabs are closed
- **Stale Data Prevention**: Automatic cleanup of outdated work data
- **German Error Messaging**: "Bitte öffnen Sie die Bedatime-Seite und lassen Sie sie geöffnet."
- **Data Integrity**: Multiple validation points prevent showing old data
- **Graceful Degradation**: Clear user guidance when no tracking tabs available

### Performance Optimization
- **Efficient DOM Parsing**: Optimized selectors for target elements
- **Minimal Resource Usage**: Lightweight background processing
- **Smart Updates**: Only recalculate when data changes detected
- **Responsive UI**: Smooth scrolling and animations

## Bug Fixes & Improvements (Latest Version)

### Refresh Button Reliability
- **Issue Fixed**: Refresh button error on subsequent clicks
- **Solution**: Enhanced error handling with timeout protection and page compatibility checking
- **Improvement**: Automatic fresh data loading on popup open eliminates need for manual refresh

### Active Session Calculation Enhancement
- **Issue Fixed**: Inaccurate active session time calculation
- **Solution**: Improved algorithm to find most recent incomplete session
- **Enhancement**: Real-time updates every 15 seconds during active work periods

### Automatic Data Loading
- **Feature Added**: Popup automatically loads current work data on open
- **Benefit**: Eliminates manual refresh requirement for immediate data visibility
- **Implementation**: Force fresh data retrieval on popup initialization

### UI Layout Optimization
- **Height Increased**: From 520px to 580px for better content visibility
- **Padding Reduced**: Optimized spacing throughout interface
- **Session Items**: Compact design allows more entries to be visible
- **Scrolling Improved**: Single container with better scroll behavior

### Error Handling Enhancement
- **German Error Messages**: Localized error feedback
- **Page Compatibility**: Checks for required elements before showing errors
- **Timeout Protection**: Prevents hanging on content script communication
- **Graceful Degradation**: Fallback to stored data when fresh data unavailable

Testing Strategy
Test Cases
Parse completed sessions correctly
Handle ongoing sessions with empty end times
Detect work status from button text
Calculate time accurately across different scenarios
Handle page reloads and data persistence
Real-time updates during active work sessions

Edge Cases
Multiple incomplete sessions
Sessions spanning midnight
Invalid time formats
Missing HTML elements
Network connectivity issues