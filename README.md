# Bedalator - Daily Work Time Calculator

A Chrome extension that automatically calculates daily work hours by parsing HTML time data from work tracking systems. Bedalator monitors both completed work sessions and ongoing work time with real-time updates.

## 🚀 Features

### Core Functionality
- **Automatic Time Parsing**: Extracts work session data from HTML tables (`#DataGridVorhandeneBuchungen`)
- **Real-time Tracking**: Monitors ongoing work sessions with automatic 30-second updates
- **Status Detection**: Automatically detects work status from button text ("Gehen"/"Kommen")
- **Daily Totals**: Calculates total daily work time including active sessions
- **Session History**: Displays all work sessions for the current day with start/end times
- **Data Persistence**: Stores work data across browser sessions using Chrome Storage API
- **Cross-Tab Functionality**: Continues tracking even when time tracking tab is not active

### User Interface
- **Modern Black-on-Black Design**: Professional dark theme interface (#0a0a0a background)
- **German Language Interface**: Complete German localization ("Gesamte Tageszeit", "Aktuelle Sitzung")
- **Status Indicators**: Animated visual indicators (🟢 Aktiv, 🔴 Inaktiv, ⚪ Unbekannt)
- **Live Updates**: Real-time session timers with automatic refresh every 30 seconds
- **Optimized Layout**: 360x650px popup with scrollable session list
- **Auto-Loading**: Automatically loads fresh data when popup opens

### Technical Features
- **Chrome Extension Manifest V3**: Latest Chrome extension standards with service worker
- **Background Processing**: Service worker for data persistence and badge updates
- **Content Script Integration**: Seamless page monitoring with DOM change detection
- **Error Handling**: Robust German error messages and page compatibility checking
- **Cross-session Persistence**: Data survives page reloads and browser restarts
- **Intelligent Updates**: Visibility-aware update frequency for performance optimization

## 📋 Requirements

- Google Chrome browser (version 88 or later)
- Access to work tracking system with compatible HTML structure
- Chrome extension developer mode (for installation)

## 🔧 Installation

### For End Users

1. **Download the Extension**
   - Download the latest release from the releases page
   - Or clone this repository: `git clone https://github.com/your-username/bedalator.git`

2. **Install in Chrome**
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right corner
   - Click "Load unpacked" and select the Bedalator folder
   - The extension icon should appear in your Chrome toolbar

3. **Icons Included**
   - Professional PNG icon files are included in the `icons/` directory:
     - `icon16.png` (16x16 pixels) - Browser toolbar
     - `icon32.png` (32x32 pixels) - Additional size
     - `icon48.png` (48x48 pixels) - Extension management
     - `icon128.png` (128x128 pixels) - Chrome Web Store
   - Icons feature a modern design suitable for professional environments

### For Developers

1. **Clone Repository**
   ```bash
   git clone https://github.com/your-username/bedalator.git
   cd bedalator
   ```

2. **Load in Chrome**
   - Follow the end user installation steps above
   - Enable "Developer mode" for debugging capabilities

## 📚 Documentation

For detailed information about the project:
- **[PROJECT_STRUCTURE.md](ai_docs/PROJECT_STRUCTURE.md)**: Comprehensive project structure and file organization
- **[ai_docs/DEVELOPMENT.md](ai_docs/DEVELOPMENT.md)**: Technical development guide and implementation details
- **[specs/init.md](specs/init.md)**: Original project specifications and implementation status

## 📖 Usage Guide

### Getting Started

1. **Navigate to Work Tracking Page**
   - Open your work tracking system in Chrome
   - The page should contain time tracking elements

2. **Verify Detection**
   - Click the Bedalator extension icon
   - Check if work sessions are detected
   - Status indicator should show current work state

3. **Monitor Work Time**
   - Extension automatically updates every minute
   - View real-time totals in the popup
   - Check session history and daily summaries

### Understanding the Interface

#### Status Indicators
- 🟢 **Aktiv**: Currently working (button shows "Gehen")
- 🔴 **Inaktiv**: Not working (button shows "Kommen")
- ⚪ **Unbekannt**: Status unknown or page not compatible

#### Time Displays
- **Gesamte Tageszeit**: Sum of all completed and ongoing sessions
- **Aktuelle Sitzung**: Duration of active work session (shown only when working)
- **Aktuelle Sitzungen**: List of all work sessions for the current day with start/end times

#### Automatic Features
- **Auto-Refresh**: Automatically updates every 30 seconds
- **Auto-Loading**: Fresh data loads immediately when popup opens
- **Cross-Tab Tracking**: Continues monitoring even when tab is not active
- **Data Persistence**: Automatically stores work history for 30 days

### Supported Work Tracking Systems

The extension looks for specific HTML elements:

- **Time Data Table**: `#DataGridVorhandeneBuchungen table tbody`
- **Status Buttons**: `#divTerminalbuttons .dx-button-text`
- **Button Text**: "Gehen" (working) / "Kommen" (not working)

## 🏗️ Technical Architecture

### File Structure
```
bedalator/
├── manifest.json          # Extension configuration
├── content.js             # Content script for HTML parsing
├── background.js           # Service worker for data persistence
├── popup.html             # User interface layout
├── popup.js               # Popup logic and communication
├── popup.css              # Styling for popup interface
├── icons/                 # Extension icons
│   ├── icon16.png         # 16x16 toolbar icon
│   ├── icon32.png         # 32x32 icon
│   ├── icon48.png         # 48x48 extension management icon
│   └── icon128.png        # 128x128 Chrome Web Store icon
├── ai_docs/               # Development documentation
│   └── DEVELOPMENT.md     # Detailed development guide
├── specs/                 # Project specifications
│   └── init.md           # Initial project specifications
├── .prompts/              # AI context and prompts
│   └── context_prime.md   # Context primer for AI assistance
├── .idea/                 # IDE configuration files
├── PROJECT_STRUCTURE.md   # Detailed project structure documentation
└── README.md              # This file
```

### Core Components

#### Content Script (`content.js`)
- Monitors work tracking page for time data
- Parses HTML tables for session information
- Detects work status from button text
- Calculates work time totals
- Sends data to background script

#### Background Script (`background.js`)
- Manages extension lifecycle
- Handles data persistence using Chrome storage
- Maintains work history
- Updates extension badge and title
- Cleans up old data automatically

#### Popup Interface (`popup.html`, `popup.js`, `popup.css`)
- Displays work time information
- Shows real-time session updates
- Provides work history view
- Handles user interactions
- Communicates with content and background scripts

### Key Functions

#### BedalatorTimeTracker Class (content.js)
```javascript
extractTimeData()           // Parse time pairs from HTML table
getCurrentWorkStatus()      // Detect work status from buttons ("Gehen"/"Kommen")
calculateDailyTotal()       // Calculate total work time including active sessions
getCurrentSessionDuration() // Calculate ongoing session duration
collectWorkData()          // Gather all work information
timeToMinutes()            // Convert HH:MM format to minutes
minutesToTime()            // Convert minutes back to HH:MM format
```

#### BedalatorBackground Class (background.js)
```javascript
handleWorkDataUpdate()     // Process data updates from content script
updateBadge()             // Update extension badge with total time
storeDailyHistory()       // Maintain work history in Chrome storage
cleanupOldHistory()       // Remove data older than 30 days
getDailySummary()         // Get 7-day work summary
```

#### BedalatorPopup Class (popup.js)
```javascript
loadWorkData()            // Load and display work data
updateUI()                // Update interface with current data
validateAndLoadData()     // Check for tracking tabs and load data
startAutoRefresh()        // Begin automatic 30-second updates
```

## 🧪 Testing

### Manual Testing

1. **Basic Functionality**
   - Load extension on work tracking page
   - Verify time data extraction
   - Check status detection accuracy
   - Confirm real-time updates

2. **Edge Cases**
   - Sessions spanning midnight
   - Multiple incomplete sessions
   - Invalid time formats
   - Missing HTML elements
   - Network connectivity issues

3. **Data Persistence**
   - Refresh page and verify data retention
   - Close/reopen browser
   - Clear browser data and test recovery

### Automated Testing

Currently, the extension relies on manual testing. Future improvements could include:
- Unit tests for time calculation functions
- Integration tests for Chrome extension APIs
- End-to-end tests for user workflows

## 🔍 Troubleshooting

### Common Issues

#### Extension Not Working
- **Check Developer Mode**: Ensure Chrome developer mode is enabled
- **Verify Page Compatibility**: Confirm page has required HTML elements
- **Check Console**: Look for error messages in browser console
- **Reload Extension**: Try disabling and re-enabling the extension

#### No Time Data Detected
- **Inspect Page Elements**: Verify HTML structure matches expected selectors
- **Check Network**: Ensure page is fully loaded
- **Review Console Logs**: Look for parsing errors
- **Try Manual Refresh**: Click the refresh button in popup

#### Incorrect Time Calculations
- **Verify Time Format**: Ensure times are in HH:MM format
- **Check Session Data**: Review session list for accuracy
- **Clear and Restart**: Clear data and let extension re-parse

#### Data Not Persisting
- **Check Storage Permissions**: Verify extension has storage permissions
- **Browser Storage**: Ensure browser allows extension storage
- **Clear Corrupted Data**: Try clearing all data and restarting

### Debug Mode

1. **Enable Developer Mode**
   - Go to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Inspect views: background page" for background script debugging

2. **Content Script Debugging**
   - Open browser developer tools (F12)
   - Check console for Bedalator log messages
   - Inspect network requests and DOM changes

3. **Storage Inspection**
   - Go to `chrome://extensions/`
   - Click extension details
   - Check "Storage" section for stored data

## 🤝 Contributing

### Development Setup

1. **Fork Repository**
   ```bash
   git fork https://github.com/your-username/bedalator.git
   cd bedalator
   ```

2. **Make Changes**
   - Edit source files as needed
   - Test changes thoroughly
   - Follow existing code style

3. **Submit Pull Request**
   - Create feature branch
   - Commit changes with clear messages
   - Submit pull request with description

### Code Style Guidelines

- Use clear, descriptive variable names
- Add comments for complex logic
- Follow existing indentation and formatting
- Handle errors gracefully
- Test edge cases thoroughly

### Reporting Issues

When reporting bugs or requesting features:
- Provide detailed description
- Include steps to reproduce
- Share browser and extension version
- Attach relevant screenshots or logs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Chrome Extension documentation and examples
- Work tracking system integration requirements
- Community feedback and testing

## 🔒 Privacy & Security

### Data Handling
- **Local Storage Only**: All data is stored locally in your browser
- **No External Servers**: No data is sent to external servers
- **No Personal Information**: Only work time data is collected
- **User Control**: You can clear all data at any time

### Permissions
- **Storage**: Required for saving work data across sessions
- **Active Tab**: Required for reading work tracking page content
- **Host Permissions**: Required for content script injection

### Security Considerations
- Extension only activates on pages with work tracking elements
- No sensitive data is transmitted or stored externally
- Regular cleanup of old data (30+ days)
- No tracking or analytics

## 🚀 Roadmap

### Planned Features
- **Export Functionality**: Export work data to CSV/Excel
- **Custom Time Formats**: Support for different time formats
- **Multiple Site Support**: Support for various work tracking systems
- **Break Time Tracking**: Track break durations
- **Weekly/Monthly Reports**: Extended reporting periods
- **Notifications**: Alerts for long work sessions
- **Dark Mode**: Dark theme support
- **Keyboard Shortcuts**: Quick access shortcuts

### Technical Improvements
- **Unit Testing**: Comprehensive test suite
- **Performance Optimization**: Reduced memory usage
- **Better Error Handling**: More robust error recovery
- **Accessibility**: Screen reader and keyboard navigation support
- **Internationalization**: Multi-language support

## 📊 Screenshots

*Note: Add actual screenshots here when available*

### Main Popup Interface
- Total daily work time display
- Current session timer
- Work status indicator
- Session list

### Work History View
- 7-day work summary
- Daily session counts
- Time trends

### Settings and Controls
- Data management options
- Refresh and clear functions

## 🔧 Configuration

### Advanced Settings

The extension can be customized by modifying the source code:

#### Time Update Intervals
```javascript
// In content.js - Background monitoring frequency
const UPDATE_INTERVAL = 30000; // 30 seconds

// In popup.js - Auto-refresh interval
const AUTO_REFRESH_INTERVAL = 30000; // 30 seconds

// In background.js - Badge update frequency
const BADGE_UPDATE_INTERVAL = 60000; // 1 minute
```

#### HTML Selectors
```javascript
// In content.js - Selectors for work tracking system
const TIME_TABLE_SELECTOR = '#DataGridVorhandeneBuchungen tbody tr:not(.dx-freespace-row)';
const STATUS_BUTTON_SELECTOR = '#divTerminalbuttons .dx-button-text';
const TIME_TABLE_CONTAINER = '#DataGridVorhandeneBuchungen';
const STATUS_CONTAINER = '#divTerminalbuttons';
```

#### Data Retention
```javascript
// In background.js - History retention period
const HISTORY_RETENTION_DAYS = 30;
const MAX_DAILY_ENTRIES = 100; // Maximum sessions per day
```

## 📞 Support

For support, questions, or feedback:
- **GitHub Issues**: Create an issue for bugs or feature requests
- **Documentation**: Check this README and inline code comments
- **Troubleshooting**: Review the troubleshooting section above
- **Community**: Check existing issues for similar problems

### Getting Help

When seeking help, please provide:
1. Chrome version and operating system
2. Extension version
3. Detailed description of the issue
4. Steps to reproduce the problem
5. Any error messages from the console
6. Screenshots if applicable

---

**Version**: 1.0.0
**Last Updated**: December 2024
**Compatibility**: Chrome 88+ (Manifest V3)
**License**: MIT
**Status**: Production Ready ✅
**Language**: German Interface
**Architecture**: Chrome Extension with Service Worker
